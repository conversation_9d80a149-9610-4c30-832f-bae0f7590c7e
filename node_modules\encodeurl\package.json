{"_from": "encodeurl@^2.0.0", "_id": "encodeurl@2.0.0", "_inBundle": false, "_integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "_location": "/encodeurl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "encodeurl@^2.0.0", "name": "encodeurl", "escapedName": "encodeurl", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/express", "/finalhandler", "/send", "/serve-static"], "_resolved": "https://registry.npmmirror.com/encodeurl/-/encodeurl-2.0.0.tgz", "_shasum": "7b8ea898077d7e409d3ac45474ea38eaf0857a58", "_spec": "encodeurl@^2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "devDependencies": {"eslint": "5.11.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "2.5.3"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/encodeurl#readme", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "name": "encodeurl", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/encodeurl.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "2.0.0"}