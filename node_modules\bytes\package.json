{"_from": "bytes@^3.1.2", "_id": "bytes@3.1.2", "_inBundle": false, "_integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "_location": "/bytes", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bytes@^3.1.2", "name": "bytes", "escapedName": "bytes", "rawSpec": "^3.1.2", "saveSpec": null, "fetchSpec": "^3.1.2"}, "_requiredBy": ["/body-parser", "/raw-body"], "_resolved": "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz", "_shasum": "8b0beeb98605adf1b128fa4386403c009e0221a5", "_spec": "bytes@^3.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\body-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "deprecated": false, "description": "Utility to parse a string bytes to bytes and vice-versa", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "homepage": "https://github.com/visionmedia/bytes.js#readme", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "license": "MIT", "name": "bytes", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "3.1.2"}