{"_from": "type-is@^2.0.1", "_id": "type-is@2.0.1", "_inBundle": false, "_integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "_location": "/type-is", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "type-is@^2.0.1", "name": "type-is", "escapedName": "type-is", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://registry.npmmirror.com/type-is/-/type-is-2.0.1.tgz", "_shasum": "64f6cf03f92fce4015c2b224793f6bdd4b068c97", "_spec": "type-is@^2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "deprecated": false, "description": "Infer the content-type of a request.", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/type-is#readme", "keywords": ["content", "type", "checking"], "license": "MIT", "name": "type-is", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/"}, "version": "2.0.1"}