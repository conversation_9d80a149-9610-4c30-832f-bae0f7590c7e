{"_from": "merge-descriptors@^2.0.0", "_id": "merge-descriptors@2.0.0", "_inBundle": false, "_integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "_location": "/merge-descriptors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "merge-descriptors@^2.0.0", "name": "merge-descriptors", "escapedName": "merge-descriptors", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "_shasum": "ea922f660635a2249ee565e0449f951e6b603808", "_spec": "merge-descriptors@^2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Merge objects using their property descriptors", "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "keywords": ["merge", "descriptors", "object", "property", "properties", "merging", "getter", "setter"], "license": "MIT", "main": "./index.js", "name": "merge-descriptors", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "scripts": {"test": "xo && ava"}, "sideEffects": false, "types": "./index.d.ts", "version": "2.0.0", "xo": {"rules": {"unicorn/prefer-module": "off"}}}