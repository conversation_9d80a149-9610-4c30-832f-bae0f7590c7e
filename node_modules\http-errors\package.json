{"_from": "http-errors@^2.0.0", "_id": "http-errors@2.0.0", "_inBundle": false, "_integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "_location": "/http-errors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-errors@^2.0.0", "name": "http-errors", "escapedName": "http-errors", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/body-parser", "/express", "/raw-body", "/send"], "_resolved": "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz", "_shasum": "b7774a1486ef73cf7667ac9ae0858c012c57b9d3", "_spec": "http-errors@^2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "deprecated": false, "description": "Create HTTP error objects", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/jshttp/http-errors#readme", "keywords": ["http", "error"], "license": "MIT", "name": "http-errors", "repository": {"type": "git", "url": "git+https://github.com/jshttp/http-errors.git"}, "scripts": {"lint": "eslint . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "2.0.0"}