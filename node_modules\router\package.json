{"_from": "router@^2.2.0", "_id": "router@2.2.0", "_inBundle": false, "_integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "_location": "/router", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "router@^2.2.0", "name": "router", "escapedName": "router", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/router/-/router-2.2.0.tgz", "_shasum": "019be620b711c87641167cc79b99090f00b146ef", "_spec": "router@^2.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "deprecated": false, "description": "Simple middleware-style router", "devDependencies": {"finalhandler": "^2.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "run-series": "^1.1.9", "standard": "^17.1.0", "supertest": "6.3.3"}, "engines": {"node": ">= 18"}, "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/router#readme", "license": "MIT", "name": "router", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/router.git"}, "scripts": {"lint": "standard", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=text npm test", "test:debug": "mocha --reporter spec --bail --check-leaks test/ --inspect --inspect-brk", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "2.2.0"}