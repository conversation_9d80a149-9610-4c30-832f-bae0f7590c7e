{"_from": "finalhandler@^2.1.0", "_id": "finalhandler@2.1.0", "_inBundle": false, "_integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "_location": "/finalhandler", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "finalhandler@^2.1.0", "name": "finalhandler", "escapedName": "finalhandler", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/finalhandler/-/finalhandler-2.1.0.tgz", "_shasum": "72306373aa89d05a8242ed569ed86a1bff7c561f", "_spec": "finalhandler@^2.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "bundleDependencies": false, "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "deprecated": false, "description": "Node.js final http responder", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^11.0.1", "nyc": "^17.1.0", "supertest": "^7.0.0"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/pillarjs/finalhandler#readme", "license": "MIT", "name": "finalhandler", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/finalhandler.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "version": "2.1.0"}