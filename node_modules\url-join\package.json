{"_from": "url-join@^4.0.1", "_id": "url-join@4.0.1", "_inBundle": false, "_integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "_location": "/url-join", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "url-join@^4.0.1", "name": "url-join", "escapedName": "url-join", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/url-join/-/url-join-4.0.1.tgz", "_shasum": "b642e21a2646808ffa178c4c5fda39844e12cde7", "_spec": "url-join@^4.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "bugs": {"url": "https://github.com/jfromaniello/url-join/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Join urls and normalize as in path.join.", "devDependencies": {"conventional-changelog": "^1.1.10", "mocha": "^3.2.0", "should": "~1.2.1"}, "homepage": "https://github.com/jfromaniello/url-join#readme", "keywords": ["url", "join"], "license": "MIT", "main": "lib/url-join.js", "name": "url-join", "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "scripts": {"test": "mocha --require should"}, "version": "4.0.1"}