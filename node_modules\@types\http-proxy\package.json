{"_from": "@types/http-proxy@^1.17.15", "_id": "@types/http-proxy@1.17.16", "_inBundle": false, "_integrity": "sha512-sdWoUajOB1cd0A8cRRQ1cfyWNbmFKLAqBB89Y8x5iYyG/mkJHc0YUH8pdWBy2omi9qtCpiIgGjuwO0dQST2l5w==", "_location": "/@types/http-proxy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/http-proxy@^1.17.15", "name": "@types/http-proxy", "escapedName": "@types%2fhttp-proxy", "scope": "@types", "rawSpec": "^1.17.15", "saveSpec": null, "fetchSpec": "^1.17.15"}, "_requiredBy": ["/http-proxy-middleware"], "_resolved": "https://registry.npmmirror.com/@types/http-proxy/-/http-proxy-1.17.16.tgz", "_shasum": "dee360707b35b3cc85afcde89ffeebff7d7f9240", "_spec": "@types/http-proxy@^1.17.15", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-proxy-middleware", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Maxime LUCE", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Raigen"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jabreu610"}, {"name": "<PERSON>", "url": "https://github.com/bodinsamuel"}], "dependencies": {"@types/node": "*"}, "deprecated": false, "description": "TypeScript definitions for http-proxy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-proxy", "license": "MIT", "main": "", "name": "@types/http-proxy", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-proxy"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "9485ba96f8d1f5becbf3fb9727c6870f2ee1e47b103bc22d93989a5c33bdd8d7", "version": "1.17.16"}