{"_from": "html-encoding-sniffer@^3.0.0", "_id": "html-encoding-sniffer@3.0.0", "_inBundle": false, "_integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "_location": "/html-encoding-sniffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "html-encoding-sniffer@^3.0.0", "name": "html-encoding-sniffer", "escapedName": "html-encoding-sniffer", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "_shasum": "2cb1a8cf0db52414776e5b2a7a04d5dd98158de9", "_spec": "html-encoding-sniffer@^3.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/html-encoding-sniffer/issues"}, "bundleDependencies": false, "dependencies": {"whatwg-encoding": "^2.0.0"}, "deprecated": false, "description": "Sniff the encoding from a HTML byte stream", "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}, "files": ["lib/"], "homepage": "https://github.com/jsdom/html-encoding-sniffer#readme", "keywords": ["encoding", "html"], "license": "MIT", "main": "lib/html-encoding-sniffer.js", "name": "html-encoding-sniffer", "repository": {"type": "git", "url": "git+https://github.com/jsdom/html-encoding-sniffer.git"}, "scripts": {"lint": "eslint .", "test": "mocha"}, "version": "3.0.0"}