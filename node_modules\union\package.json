{"_from": "union@~0.5.0", "_id": "union@0.5.0", "_inBundle": false, "_integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "_location": "/union", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "union@~0.5.0", "name": "union", "escapedName": "union", "rawSpec": "~0.5.0", "saveSpec": null, "fetchSpec": "~0.5.0"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/union/-/union-0.5.0.tgz", "_shasum": "b2c11be84f60538537b846edb9ba266ba0090075", "_spec": "union@~0.5.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/flatiron/union/issues"}, "bundleDependencies": false, "dependencies": {"qs": "^6.4.0"}, "deprecated": false, "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "devDependencies": {"connect": "2.22.x", "director": "1.x.x", "ecstatic": "0.5.x", "request": "2.29.x", "vows": "0.8.0"}, "engines": {"node": ">= 0.8.0"}, "homepage": "https://github.com/flatiron/union#readme", "main": "./lib", "maintainers": [{"name": "dscape", "email": "<EMAIL>"}], "name": "union", "repository": {"type": "git", "url": "git+ssh://**************/flatiron/union.git"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "version": "0.5.0"}