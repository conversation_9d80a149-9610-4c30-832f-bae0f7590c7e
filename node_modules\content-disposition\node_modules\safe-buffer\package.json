{"_from": "safe-buffer@5.2.1", "_id": "safe-buffer@5.2.1", "_inBundle": false, "_integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "_location": "/content-disposition/safe-buffer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "safe-buffer@5.2.1", "name": "safe-buffer", "escapedName": "safe-buffer", "rawSpec": "5.2.1", "saveSpec": null, "fetchSpec": "5.2.1"}, "_requiredBy": ["/content-disposition"], "_resolved": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "_shasum": "1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6", "_spec": "safe-buffer@5.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\content-disposition", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Safer Node.js Buffer API", "devDependencies": {"standard": "*", "tape": "^5.0.0"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "name": "safe-buffer", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test/*.js"}, "types": "index.d.ts", "version": "5.2.1"}