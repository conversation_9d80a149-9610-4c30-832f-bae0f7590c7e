{"_from": "secure-compare@3.0.1", "_id": "secure-compare@3.0.1", "_inBundle": false, "_integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "_location": "/secure-compare", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "secure-compare@3.0.1", "name": "secure-compare", "escapedName": "secure-compare", "rawSpec": "3.0.1", "saveSpec": null, "fetchSpec": "3.0.1"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/secure-compare/-/secure-compare-3.0.1.tgz", "_shasum": "f1a0329b308b221fae37b9974f3d578d0ca999e3", "_spec": "secure-compare@3.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/vdemedes/secure-compare/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Securely compare two strings, copied from cryptiles", "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.1"}, "homepage": "https://github.com/vdemedes/secure-compare", "keywords": ["secure", "compare"], "license": "MIT", "main": "index.js", "name": "secure-compare", "repository": {"type": "git", "url": "git+https://github.com/vdemedes/secure-compare.git"}, "scripts": {"test": "mocha test"}, "version": "3.0.1"}