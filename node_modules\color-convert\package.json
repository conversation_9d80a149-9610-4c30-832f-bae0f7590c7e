{"_from": "color-convert@^2.0.1", "_id": "color-convert@2.0.1", "_inBundle": false, "_integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "_location": "/color-convert", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "color-convert@^2.0.1", "name": "color-convert", "escapedName": "color-convert", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/ansi-styles"], "_resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "_shasum": "72d3a68d598c9bdb3af2ad1e84f21d896abd4de3", "_spec": "color-convert@^2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\ansi-styles", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Qix-/color-convert/issues"}, "bundleDependencies": false, "dependencies": {"color-name": "~1.1.4"}, "deprecated": false, "description": "Plain color conversion functions", "devDependencies": {"chalk": "^2.4.2", "xo": "^0.24.0"}, "engines": {"node": ">=7.0.0"}, "files": ["index.js", "conversions.js", "route.js"], "homepage": "https://github.com/Qix-/color-convert#readme", "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "license": "MIT", "name": "color-convert", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color-convert.git"}, "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "version": "2.0.1", "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}}