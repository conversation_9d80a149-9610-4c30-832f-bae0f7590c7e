{"_from": "corser@^2.0.1", "_id": "corser@2.0.1", "_inBundle": false, "_integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "_location": "/corser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "corser@^2.0.1", "name": "corser", "escapedName": "corser", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/corser/-/corser-2.0.1.tgz", "_shasum": "8eda252ecaab5840dcd975ceb90d9370c819ff87", "_spec": "corser@^2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/agrueneberg/Corser/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A highly configurable, middleware compatible implementation of CORS.", "devDependencies": {"expect.js": "0.1.x", "mocha": "1.3.x"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/agrueneberg/Corser#readme", "keywords": ["cors", "cross-origin resource sharing", "connect", "express", "middleware"], "license": "MIT", "main": "./lib/corser.js", "name": "corser", "repository": {"type": "git", "url": "git+https://github.com/agrueneberg/Corser.git"}, "scripts": {"test": "mocha"}, "version": "2.0.1"}