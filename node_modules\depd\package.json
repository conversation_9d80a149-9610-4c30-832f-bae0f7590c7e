{"_from": "depd@2.0.0", "_id": "depd@2.0.0", "_inBundle": false, "_integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "_location": "/depd", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "depd@2.0.0", "name": "depd", "escapedName": "depd", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/http-errors", "/router"], "_resolved": "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz", "_shasum": "b696163cc757560d09cf22cc8fad1571b79e76df", "_spec": "depd@2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-errors", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/browser/index.js", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Deprecate all the things", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "5.7.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0", "safe-buffer": "5.1.2", "uid-safe": "2.1.5"}, "engines": {"node": ">= 0.8"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "keywords": ["deprecate", "deprecated"], "license": "MIT", "name": "depd", "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter spec test/ && istanbul report lcovonly text-summary", "test-cov": "istanbul cover --print=none node_modules/mocha/bin/_mocha -- --reporter dot test/ && istanbul report lcov text-summary"}, "version": "2.0.0"}