{"_from": "mime@^1.6.0", "_id": "mime@1.6.0", "_inBundle": false, "_integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "_location": "/mime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mime@^1.6.0", "name": "mime", "escapedName": "mime", "rawSpec": "^1.6.0", "saveSpec": null, "fetchSpec": "^1.6.0"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz", "_shasum": "32cd9e5c64553bd58d19a568af452acff04981b1", "_spec": "mime@^1.6.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}, "bin": {"mime": "cli.js"}, "bugs": {"url": "https://github.com/broofa/node-mime/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/bentomas"}], "dependencies": {}, "deprecated": false, "description": "A comprehensive library for mime-type mapping", "devDependencies": {"github-release-notes": "0.13.1", "mime-db": "1.31.0", "mime-score": "1.1.0"}, "engines": {"node": ">=4"}, "homepage": "https://github.com/broofa/node-mime#readme", "keywords": ["util", "mime"], "license": "MIT", "main": "mime.js", "name": "mime", "repository": {"url": "git+https://github.com/broofa/node-mime.git", "type": "git"}, "scripts": {"changelog": "gren changelog --tags=all --generate --override", "prepare": "node src/build.js", "test": "node src/test.js"}, "version": "1.6.0"}