{"_from": "cookie-signature@^1.2.1", "_id": "cookie-signature@1.2.2", "_inBundle": false, "_integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "_location": "/cookie-signature", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cookie-signature@^1.2.1", "name": "cookie-signature", "escapedName": "cookie-signature", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.2.2.tgz", "_shasum": "57c7fc3cc293acab9fec54d73e15690ebe4a1793", "_spec": "cookie-signature@^1.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Sign and unsign cookies", "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">=6.6.0"}, "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "keywords": ["cookie", "sign", "unsign"], "license": "MIT", "main": "index.js", "name": "cookie-signature", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "scripts": {"test": "mocha --require should --reporter spec"}, "version": "1.2.2"}