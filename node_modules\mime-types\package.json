{"_from": "mime-types@^3.0.0", "_id": "mime-types@3.0.1", "_inBundle": false, "_integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "_location": "/mime-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mime-types@^3.0.0", "name": "mime-types", "escapedName": "mime-types", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/accepts", "/express", "/send", "/type-is"], "_resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-3.0.1.tgz", "_shasum": "b1d94d6997a9b32fd69ebaed0db73de8acb519ce", "_spec": "mime-types@^3.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-db": "^1.54.0"}, "deprecated": false, "description": "The ultimate javascript content-type utility.", "devDependencies": {"eslint": "8.33.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js", "mimeScore.js"], "homepage": "https://github.com/jshttp/mime-types#readme", "keywords": ["mime", "types"], "license": "MIT", "name": "mime-types", "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "3.0.1"}