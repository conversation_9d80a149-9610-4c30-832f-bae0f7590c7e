{"_from": "portfinder@^1.0.28", "_id": "portfinder@1.0.37", "_inBundle": false, "_integrity": "sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==", "_location": "/portfinder", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "portfinder@^1.0.28", "name": "portfinder", "escapedName": "portfinder", "rawSpec": "^1.0.28", "saveSpec": null, "fetchSpec": "^1.0.28"}, "_requiredBy": ["/http-server"], "_resolved": "https://registry.npmmirror.com/portfinder/-/portfinder-1.0.37.tgz", "_shasum": "92b754ef89a11801c8efe4b0e5cd845b0064c212", "_spec": "portfinder@^1.0.28", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-server", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/http-party/node-portfinder/issues"}, "bundleDependencies": false, "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "deprecated": false, "description": "A simple tool to find an open port on the current machine", "devDependencies": {"jest": "^29.7.0"}, "engines": {"node": ">= 10.12"}, "files": ["lib"], "homepage": "https://github.com/http-party/node-portfinder#readme", "keywords": ["http", "ports", "utilities"], "license": "MIT", "main": "./lib/portfinder", "name": "portfinder", "repository": {"type": "git", "url": "git+ssh://**************/http-party/node-portfinder.git"}, "scripts": {"test": "jest --runInBand"}, "types": "./lib/portfinder.d.ts", "version": "1.0.37"}