{"_from": "serve-static@^2.2.0", "_id": "serve-static@2.2.0", "_inBundle": false, "_integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "_location": "/serve-static", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "serve-static@^2.2.0", "name": "serve-static", "escapedName": "serve-static", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/serve-static/-/serve-static-2.2.0.tgz", "_shasum": "9c02564ee259bdd2251b82d659a2e7e1938d66f9", "_spec": "serve-static@^2.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "bundleDependencies": false, "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "deprecated": false, "description": "Serve static files", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "^6.3.4"}, "engines": {"node": ">= 18"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/serve-static#readme", "license": "MIT", "name": "serve-static", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-static.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "2.2.0"}