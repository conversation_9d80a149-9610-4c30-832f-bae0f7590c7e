{"_from": "send@^1.1.0", "_id": "send@1.2.0", "_inBundle": false, "_integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "_location": "/send", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "send@^1.1.0", "name": "send", "escapedName": "send", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/express", "/serve-static"], "_resolved": "https://registry.npmmirror.com/send/-/send-1.2.0.tgz", "_shasum": "32a7554fb777b831dfa828370f773a3808d37212", "_spec": "send@^1.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/send/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "deprecated": false, "description": "Better streaming static file server with Range and conditional-GET support", "devDependencies": {"after": "^0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "6.2.2"}, "engines": {"node": ">= 18"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/send#readme", "keywords": ["static", "file", "server"], "license": "MIT", "name": "send", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/send.git"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.2.0"}