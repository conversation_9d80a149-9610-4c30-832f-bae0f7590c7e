{"_from": "is-promise@^4.0.0", "_id": "is-promise@4.0.0", "_inBundle": false, "_integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==", "_location": "/is-promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-promise@^4.0.0", "name": "is-promise", "escapedName": "is-promise", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/router"], "_resolved": "https://registry.npmmirror.com/is-promise/-/is-promise-4.0.0.tgz", "_shasum": "42ff9f84206c1991d26debf520dd5c01042dd2f3", "_spec": "is-promise@^4.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\router", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/then/is-promise/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Test whether an object looks like a promises-a+ promise", "exports": {".": [{"import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index.js"]}, "files": ["index.js", "index.mjs", "index.d.ts"], "homepage": "https://github.com/then/is-promise#readme", "license": "MIT", "main": "./index.js", "name": "is-promise", "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "scripts": {"test": "node test"}, "version": "4.0.0"}