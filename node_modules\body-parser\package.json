{"_from": "body-parser@^2.2.0", "_id": "body-parser@2.2.0", "_inBundle": false, "_integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "_location": "/body-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "body-parser@^2.2.0", "name": "body-parser", "escapedName": "body-parser", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/body-parser/-/body-parser-2.2.0.tgz", "_shasum": "f7a9656de305249a715b549b7b8fd1ab9dfddcfa", "_spec": "body-parser@^2.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "deprecated": false, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "8.34.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "^11.1.0", "nyc": "^17.1.0", "supertest": "^7.0.0"}, "engines": {"node": ">=18"}, "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.2.0"}