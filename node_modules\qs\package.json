{"_from": "qs@^6.4.0", "_id": "qs@6.14.0", "_inBundle": false, "_integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "_location": "/qs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "qs@^6.4.0", "name": "qs", "escapedName": "qs", "rawSpec": "^6.4.0", "saveSpec": null, "fetchSpec": "^6.4.0"}, "_requiredBy": ["/union"], "_resolved": "https://registry.npmmirror.com/qs/-/qs-6.14.0.tgz", "_shasum": "c63fa40680d2c5c941412a0e899c89af60c0a930", "_spec": "qs@^6.4.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\union", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"side-channel": "^1.1.0"}, "deprecated": false, "description": "A querystring parser that supports nesting and arrays, with a depth limit", "devDependencies": {"@browserify/envify": "^6.0.0", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "browserify": "^16.5.2", "bundle-collapser": "^1.4.0", "common-shakeify": "~1.0.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.7.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "glob": "=10.3.7", "has-bigints": "^1.1.0", "has-override-mistake": "^1.0.1", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "mkdirp": "^0.5.5", "mock-property": "^1.1.0", "module-deps": "^6.2.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.9.0", "unassertify": "^3.0.1"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/qs", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "qs", "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/qs.git"}, "scripts": {"dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@'>=10.2' audit --production", "prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent readme && npm run --silent lint", "readme": "evalmd README.md", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'"}, "sideEffects": false, "version": "6.14.0"}