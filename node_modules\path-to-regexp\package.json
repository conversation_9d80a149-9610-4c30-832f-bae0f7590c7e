{"_from": "path-to-regexp@^8.0.0", "_id": "path-to-regexp@8.2.0", "_inBundle": false, "_integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "_location": "/path-to-regexp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-to-regexp@^8.0.0", "name": "path-to-regexp", "escapedName": "path-to-regexp", "rawSpec": "^8.0.0", "saveSpec": null, "fetchSpec": "^8.0.0"}, "_requiredBy": ["/router"], "_resolved": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "_shasum": "73990cc29e57a3ff2a0d914095156df5db79e8b4", "_spec": "path-to-regexp@^8.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\router", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Express style path to RegExp utility", "devDependencies": {"@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2", "@types/node": "^22.7.2", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^2.1.1", "recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3"}, "engines": {"node": ">=16"}, "exports": "./dist/index.js", "files": ["dist/"], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "main": "dist/index.js", "name": "path-to-regexp", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/pillarjs/path-to-regexp.git"}, "scripts": {"bench": "vitest bench", "build": "ts-scripts build", "format": "ts-scripts format", "lint": "ts-scripts lint", "prepare": "ts-scripts install && npm run build", "size": "size-limit", "specs": "ts-scripts specs", "test": "ts-scripts test && npm run size"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "typings": "dist/index.d.ts", "version": "8.2.0"}