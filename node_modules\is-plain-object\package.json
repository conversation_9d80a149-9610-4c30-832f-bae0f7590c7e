{"_from": "is-plain-object@^5.0.0", "_id": "is-plain-object@5.0.0", "_inBundle": false, "_integrity": "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==", "_location": "/is-plain-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-plain-object@^5.0.0", "name": "is-plain-object", "escapedName": "is-plain-object", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/http-proxy-middleware"], "_resolved": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-5.0.0.tgz", "_shasum": "4427f50ab3429e9025ea7d52e9043a9ef4159344", "_spec": "is-plain-object@^5.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\http-proxy-middleware", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "deprecated": false, "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "engines": {"node": ">=0.10.0"}, "exports": {".": {"import": "./dist/is-plain-object.mjs", "require": "./dist/is-plain-object.js"}, "./package.json": "./package.json"}, "files": ["is-plain-object.d.ts", "dist"], "homepage": "https://github.com/jonschlinkert/is-plain-object", "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "license": "MIT", "main": "dist/is-plain-object.js", "module": "dist/is-plain-object.mjs", "name": "is-plain-object", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "scripts": {"build": "rollup -c", "prepare": "rollup -c", "test": "npm run test_node && npm run build && npm run test_browser", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm"}, "types": "is-plain-object.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "version": "5.0.0"}