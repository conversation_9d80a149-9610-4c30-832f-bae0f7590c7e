{"_from": "http-server", "_id": "http-server@14.1.1", "_inBundle": false, "_integrity": "sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==", "_location": "/http-server", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "http-server", "name": "http-server", "escapedName": "http-server", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/http-server/-/http-server-14.1.1.tgz", "_shasum": "d60fbb37d7c2fdff0f0fbff0d0ee6670bd285e2e", "_spec": "http-server", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test", "bin": {"http-server": "bin/http-server"}, "bugs": {"url": "https://github.com/http-party/http-server/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "brad dunbar", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "BigBlueHat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jade<PERSON><PERSON><PERSON>@jmthornton.net"}], "dependencies": {"basic-auth": "^2.0.1", "chalk": "^4.1.2", "corser": "^2.0.1", "he": "^1.2.0", "html-encoding-sniffer": "^3.0.0", "http-proxy": "^1.18.1", "mime": "^1.6.0", "minimist": "^1.2.6", "opener": "^1.5.1", "portfinder": "^1.0.28", "secure-compare": "3.0.1", "union": "~0.5.0", "url-join": "^4.0.1"}, "deprecated": false, "description": "A simple zero-configuration command-line http server", "devDependencies": {"eol": "^0.9.1", "eslint": "^4.19.1", "eslint-config-populist": "^4.2.0", "express": "^4.17.1", "request": "^2.88.2", "tap": "^14.11.0"}, "engines": {"node": ">=12"}, "files": ["lib", "bin", "doc"], "homepage": "https://github.com/http-party/http-server#readme", "keywords": ["cli", "command", "static", "http", "https", "http-server", "https-server", "server"], "license": "MIT", "main": "./lib/http-server", "man": ["./doc/http-server.1"], "name": "http-server", "preferGlobal": true, "repository": {"type": "git", "url": "git://github.com/http-party/http-server.git"}, "scripts": {"start": "node ./bin/http-server", "test": "tap --reporter=spec test/*.test.js", "test-watch": "tap --reporter=spec --watch test/*.test.js"}, "version": "14.1.1"}