{"_from": "whatwg-encoding@^2.0.0", "_id": "whatwg-encoding@2.0.0", "_inBundle": false, "_integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "_location": "/whatwg-encoding", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "whatwg-encoding@^2.0.0", "name": "whatwg-encoding", "escapedName": "whatwg-encoding", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/html-encoding-sniffer"], "_resolved": "https://registry.npmmirror.com/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "_shasum": "e7635f597fd87020858626805a2729fa7698ac53", "_spec": "whatwg-encoding@^2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\html-encoding-sniffer", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "bundleDependencies": false, "dependencies": {"iconv-lite": "0.6.3"}, "deprecated": false, "description": "Decode strings according to the WHATWG Encoding Standard", "devDependencies": {"@domenic/eslint-config": "^1.3.0", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}, "files": ["lib/"], "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "keywords": ["encoding", "whatwg"], "license": "MIT", "main": "lib/whatwg-encoding.js", "name": "whatwg-encoding", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "scripts": {"lint": "eslint .", "prepare": "node scripts/update.js", "test": "mocha"}, "version": "2.0.0"}