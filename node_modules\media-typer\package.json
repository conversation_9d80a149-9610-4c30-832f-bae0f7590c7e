{"_from": "media-typer@^1.1.0", "_id": "media-typer@1.1.0", "_inBundle": false, "_integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "_location": "/media-typer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "media-typer@^1.1.0", "name": "media-typer", "escapedName": "media-typer", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/type-is"], "_resolved": "https://registry.npmmirror.com/media-typer/-/media-typer-1.1.0.tgz", "_shasum": "6ab74b8f2d3320f2064b2a87a38e7931ff3a5561", "_spec": "media-typer@^1.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\type-is", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple RFC 6838 media type parser and formatter", "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/media-typer#readme", "license": "MIT", "name": "media-typer", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "version": "1.1.0"}