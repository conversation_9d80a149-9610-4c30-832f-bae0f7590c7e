{"_from": "negotiator@^1.0.0", "_id": "negotiator@1.0.0", "_inBundle": false, "_integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==", "_location": "/negotiator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "negotiator@^1.0.0", "name": "negotiator", "escapedName": "negotiator", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/accepts"], "_resolved": "https://registry.npmmirror.com/negotiator/-/negotiator-1.0.0.tgz", "_shasum": "b6c91bb47172d69f93cfd7c357bbb529019b5f6a", "_spec": "negotiator@^1.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\accepts", "bugs": {"url": "https://github.com/jshttp/negotiator/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "deprecated": false, "description": "HTTP content negotiation", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "homepage": "https://github.com/jshttp/negotiator#readme", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "license": "MIT", "name": "negotiator", "repository": {"type": "git", "url": "git+https://github.com/jshttp/negotiator.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/"}, "version": "1.0.0"}