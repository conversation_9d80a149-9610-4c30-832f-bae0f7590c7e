{"_from": "accepts@^2.0.0", "_id": "accepts@2.0.0", "_inBundle": false, "_integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "_location": "/accepts", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "accepts@^2.0.0", "name": "accepts", "escapedName": "accepts", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/accepts/-/accepts-2.0.0.tgz", "_shasum": "bbcf4ba5075467f3f2131eab3cffc73c2f5d7895", "_spec": "accepts@^2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "deprecated": false, "description": "Higher-level content negotiation", "devDependencies": {"deep-equal": "1.0.1", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "name": "accepts", "repository": {"type": "git", "url": "git+https://github.com/jshttp/accepts.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.0.0"}